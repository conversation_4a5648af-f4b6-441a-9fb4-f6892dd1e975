# vector.py
import chromadb
from sentence_transformers import SentenceTransformer
from transformers import AutoModel, AutoModelForCausalLM, AutoTokenizer

client = chromadb.Client()

model_name = "gpt2"
tokenizer = AutoTokenizer.from_pretrained(model_name)
model = AutoModelForCausalLM.from_pretrained(model_name)

collection = client.get_or_create_collection(name="my_documents")

Transformer_model = SentenceTransformer('all-MiniLM-L6-v2')

documents = [
    "thank you for the compliment.",
    "Hi, how may i help you?",
    "Cats are small domesticated animals.",
    "Dogs are loyal and friendly pets.",
    "The sky is blue and vast.",
    "I love programming with Python.",
    "Machine learning is transforming industries worldwide.",
    "Natural language processing helps computers understand human language.",
    "Vector databases store and retrieve data based on similarity.",
    "Embeddings represent text as numerical vectors in high-dimensional space.",
    "Neural networks are inspired by the human brain.",
    "Data science combines statistics, programming, and domain expertise.",
    "Cloud computing provides scalable resources over the internet.",
    "Artificial intelligence aims to create machines that can think like humans.",
    "Algorithms are step-by-step procedures for solving problems.",
    "Databases store and organize data for efficient retrieval.",
    "my name is <PERSON><PERSON>",
    "Hey, I'm doing good",
    "I enjoy building AI applications",
    "My favorite programming language is Python",
    "I'm interested in natural language processing",
    "Smartphones have revolutionized modern communication",
    "iPhones use iOS operating system developed by Apple",
    "Android is an open-source mobile operating system",
    "Electric cars are becoming increasingly popular",
    "Tesla manufactures electric vehicles and clean energy solutions",
    "Autonomous vehicles use sensors and AI to navigate",
    "Hybrid cars combine gasoline engines with electric motors",
    "5G technology enables faster mobile internet speeds",
    "Foldable phones represent the latest smartphone innovation",
    "Had nice chat with you. will await your next message."
]


while True:
    # Step 5: Query
    print("")
    query = input("\033[94mUser : \033[0m")
    print("")

    query_embedding = Transformer_model.encode([query]).tolist()[0]
    
    results = collection.query(
        query_embeddings=[query_embedding],
        n_results=1
    )
    
    # Step 6: Show results
    print("\033[1;92mlocalGPT :\033[0m", results['documents'][0][0])

    print("________________________________________")
    if results['documents'][0][0] == "Had nice chat with you. will await your next message.":
        break
        
