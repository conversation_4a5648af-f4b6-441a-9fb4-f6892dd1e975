# vector.py
import chromadb
from sentence_transformers import SentenceTransformer
from transformers import pipeline
import re

# Initialize ChromaDB client
client = chromadb.Client()

# Create or get collection
collection = client.get_or_create_collection(name="my_documents")

# Initialize sentence transformer model for embeddings
transformer_model = SentenceTransformer('all-MiniLM-L6-v2')

# Initialize text generation pipeline with a pretrained model
print("Loading pretrained text generation model...")
text_generator = pipeline("text-generation", model="gpt2", max_length=100, num_return_sequences=1)

def generate_documents_from_prompts(prompts, num_docs_per_prompt=2):
    """Generate documents using a pretrained model from given prompts"""
    generated_docs = []

    for prompt in prompts:
        print(f"Generating documents for prompt: '{prompt}'")
        try:
            # Generate text using the pretrained model
            generated_texts = text_generator(prompt, max_length=80, num_return_sequences=num_docs_per_prompt,
                                           do_sample=True, temperature=0.7, pad_token_id=50256)

            for generated in generated_texts:
                # Clean up the generated text
                text = generated['generated_text'].strip()
                # Remove the original prompt from the generated text if it's included
                if text.startswith(prompt):
                    text = text[len(prompt):].strip()

                # Clean up text (remove extra whitespace, newlines)
                text = re.sub(r'\s+', ' ', text).strip()

                if text and len(text) > 10:  # Only add if text is meaningful
                    generated_docs.append(text)

        except Exception as e:
            print(f"Error generating text for prompt '{prompt}': {e}")
            continue

    return generated_docs

# Define prompts for text generation
prompts = [
    "Artificial intelligence is",
    "Machine learning helps",
    "Python programming is",
    "Data science involves",
    "Neural networks can",
    "Natural language processing",
    "Computer vision enables",
    "Deep learning models",
    "Technology today allows",
    "Software development requires",
    "Cloud computing provides",
    "Cybersecurity is important because",
    "Mobile applications are",
    "Web development involves",
    "Database systems help"
]

# Generate documents using the pretrained model
print("Generating documents using pretrained model...")
documents = generate_documents_from_prompts(prompts, num_docs_per_prompt=2)

# Add some fallback documents if generation fails
if len(documents) < 5:
    fallback_docs = [
        "Thank you for the compliment.",
        "Hi, how may I help you?",
        "I'm here to assist you with your questions.",
        "Let me know if you need any help.",
        "Had nice chat with you. Will await your next message."
    ]
    documents.extend(fallback_docs)

print(f"Generated {len(documents)} documents from pretrained model.")

# Generate embeddings for all documents
print("Generating embeddings for documents...")
document_embeddings = transformer_model.encode(documents).tolist()

# Create unique IDs for each document
document_ids = [f"doc_{i}" for i in range(len(documents))]

# Add documents with their embeddings to the collection
collection.add(
    documents=documents,
    embeddings=document_embeddings,
    ids=document_ids
)

print(f"Added {len(documents)} documents to the collection.")
print("Ready for queries!")

while True:
    # Step 5: Query
    print("")
    query = input("\033[94mUser : \033[0m")
    print("")

    query_embedding = transformer_model.encode([query]).tolist()[0]
    
    results = collection.query(
        query_embeddings=[query_embedding],
        n_results=1
    )
    
    # Step 6: Show results
    print("\033[1;92mlocalGPT :\033[0m", results['documents'][0][0])

    print("________________________________________")
    if results['documents'][0][0] == "Had nice chat with you. will await your next message.":
        break
        
