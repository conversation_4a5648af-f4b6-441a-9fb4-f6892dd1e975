# vector.py
import chromadb
from sentence_transformers import SentenceTransformer

# Initialize ChromaDB client
client = chromadb.Client()

# Create or get collection
collection = client.get_or_create_collection(name="my_documents")

# Initialize sentence transformer model for embeddings
transformer_model = SentenceTransformer('all-MiniLM-L6-v2')

# Define the documents to be stored in the vector database
documents = [
    "thank you for the compliment.",
    "Hi, how may i help you?",
    "Cats are small domesticated animals.",
    "Dogs are loyal and friendly pets.",
    "The sky is blue and vast.",
    "I love programming with Python.",
    "Machine learning is transforming industries worldwide.",
    "Natural language processing helps computers understand human language.",
    "Vector databases store and retrieve data based on similarity.",
    "Embeddings represent text as numerical vectors in high-dimensional space.",
    "Neural networks are inspired by the human brain.",
    "Data science combines statistics, programming, and domain expertise.",
    "Cloud computing provides scalable resources over the internet.",
    "Artificial intelligence aims to create machines that can think like humans.",
    "Algorithms are step-by-step procedures for solving problems.",
    "Databases store and organize data for efficient retrieval.",
    "my name is <PERSON><PERSON>",
    "Hey, I'm doing good",
    "I enjoy building AI applications",
    "My favorite programming language is Python",
    "I'm interested in natural language processing",
    "Smartphones have revolutionized modern communication",
    "iPhones use iOS operating system developed by Apple",
    "Android is an open-source mobile operating system",
    "Electric cars are becoming increasingly popular",
    "Tesla manufactures electric vehicles and clean energy solutions",
    "Autonomous vehicles use sensors and AI to navigate",
    "Hybrid cars combine gasoline engines with electric motors",
    "5G technology enables faster mobile internet speeds",
    "Foldable phones represent the latest smartphone innovation",
    "Had nice chat with you. will await your next message."
]

# Generate embeddings for all documents
print("Generating embeddings for documents...")
document_embeddings = transformer_model.encode(documents).tolist()

# Create unique IDs for each document
document_ids = [f"doc_{i}" for i in range(len(documents))]

# Add documents with their embeddings to the collection
collection.add(
    documents=documents,
    embeddings=document_embeddings,
    ids=document_ids
)

print(f"Added {len(documents)} documents to the collection.")
print("Ready for queries!")

while True:
    # Step 5: Query
    print("")
    query = input("\033[94mUser : \033[0m")
    print("")

    query_embedding = transformer_model.encode([query]).tolist()[0]
    
    results = collection.query(
        query_embeddings=[query_embedding],
        n_results=1
    )
    
    # Step 6: Show results
    print("\033[1;92mlocalGPT :\033[0m", results['documents'][0][0])

    print("________________________________________")
    if results['documents'][0][0] == "Had nice chat with you. will await your next message.":
        break
        
